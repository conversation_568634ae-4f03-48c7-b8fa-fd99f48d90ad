/* General Styles */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f9f9f9;
    color: #333;
}

/* Header Styles */
h1 {
    text-align: center;
    color: #4CAF50;
    margin-top: 20px;
    padding: 0 10px;
}

h2 {
    color: #555;
    margin-top: 20px;
    padding: 0 10px;
}

/* Paragraph and Link Styles */
p,a {
    font-size: 16px;
    padding: 0 10px;
}

a {
    color: #008CBA;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

/* List Styles */
ul {
    margin: 10px 0 10px 20px;
}

li {
    margin-bottom: 5px;
}

/* Image Styles */
img {
    margin: 10px 0;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Table Styles */
table {
    width: 100%;
    margin: 10px 0;
}

th, td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    background-color: #f2f2f2;
}

/* Form Styles */
form {
    margin-top: 20px;
    padding: 10px;
    background: #f4f4f4;
    border-radius: 5px;
}

label {
    display: block;
    margin-bottom: 5px;
}

input, select, button {
    margin-bottom: 10px;
    padding: 8px;
    width: 100%;
}

button {
    background-color: #4CAF50;
    color: white;
    border: none;
    cursor: pointer;
}

button:hover {
    background-color: #45a049;
}

/* Audio and Video Styles */
audio {
    margin: 10px 0;
    width: 100%;
}

iframe {
    margin: 10px auto;
    border: none;
}

/* Details and Summary Styles */
details {
    margin: 10px 0;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: #f4f4f4;
}

summary {
    cursor: pointer;
    font-weight: bold;
}

select{
    cursor: pointer;
}

img {

    display: block;
    margin: auto;
}