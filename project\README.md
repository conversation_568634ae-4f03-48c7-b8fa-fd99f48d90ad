# Personal Website Project

A simple, modern personal website built with HTML and CSS as a final project for the web development course.

## Features

- **Responsive Design**: Works on desktop, tablet, and mobile devices
- **Modern UI**: Clean, professional design with smooth animations
- **Semantic HTML**: Uses proper HTML5 semantic elements
- **CSS Grid & Flexbox**: Modern layout techniques
- **Font Awesome Icons**: Professional icons for social media and skills
- **Google Fonts**: Beautiful typography with Inter font family

## Files Structure

```
project/
├── index.html          # Main HTML file
├── style.css           # CSS stylesheet
├── profile.jpg         # Profile image
└── README.md          # This file
```

## How to View

1. Open `index.html` in any modern web browser
2. Or use a local server for better development experience

## Customization

### Personal Information
- Edit the name "<PERSON>e" in `index.html`
- Update the description text in the hero section
- Change social media links in the header navigation

### Profile Image
- Replace `profile.jpg` with your own image
- Keep the same filename or update the `src` attribute in HTML

### Skills Section
- Modify the skills in the skills grid
- Add or remove skill cards as needed
- Update the Font Awesome icons for different technologies

### Colors & Styling
- Primary color: `#007bff` (blue)
- Background: `#f5f5f5` (light gray)
- Text: `#333` (dark gray)
- Cards: `#fff` (white)

## Technologies Used

- HTML5 (semantic elements, forms, media)
- CSS3 (flexbox, grid, animations, responsive design)
- Font Awesome 6.0 (icons)
- Google Fonts (Inter font family)

## Course Concepts Applied

- HTML semantic elements (`header`, `nav`, `main`, `section`, `footer`)
- CSS styling (colors, fonts, spacing, borders)
- Layout techniques (flexbox, grid)
- Responsive design with media queries
- Modern CSS features (transitions, transforms, box-shadow)

## Browser Compatibility

Works in all modern browsers including:
- Chrome
- Firefox
- Safari
- Edge

---

Created as a final project for Web Development Course 2025 