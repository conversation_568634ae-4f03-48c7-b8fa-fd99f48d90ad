
function onButtonClick(){
    console.log("<PERSON><PERSON> was Pressed");
    h1Helement.innerHTML = "But<PERSON> was Pressed";
}
var buttonELement = document.getElementById("btn1");
var h1Helement = document.getElementById("hdr1");
buttonELement.onclick = onButtonClick;
buttonELement.addEventListener('click', onButtonClick);
/*
1. اعمل متعير قيمته صفر
2. وصل العنوان والزر في جافاسكريبت
3. اكتب دالة فنكشن عند الضغط على الزر تزيد العداد وتغير العنوان

*/


// // if (5>3) {
// // console.log('5>3');
// // } 

// // var number = 0;
// // while (number < 10){
// // number = number +1;
// // number++;
// // number--;
// // console.log('number is ',number);
// // }
// // for (var i=10;i>0;i--){
// //     console.log('i value is: ', i); 
// // }

// var result = add2Numbers(3,4);
// console.log('result is: ', result);
// // printHello();
// function printHello(){
//     console.log("Hello 1");
//     console.log("Hello 2");
//     console.log("Hello 3");
//     console.log("Hello 4");
//     console.log("Hello 5");
//     console.log("Hello 5");
//     console.log("Hello 6");
//     console.log("Hello 7");
//     console.log("Hello 8");
// }

// function add2Numbers(num1,  num2) {
//     var HAMADA = num1 + num2;
//  return HAMADA;
// }

