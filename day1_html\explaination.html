<!-- Explain theory -> install vs code -->

<!-- Covered Tags (25): -->
<!-- 0) Tag format <TagName attributes> Content </TagName> -->
<!-- 1) html, head, body then install live server --> 
<!-- 2) div, h1, h2, h3, p -->
<!-- Task #1: h1 your name &  welcome sentence -->
<!-- 3) ol, ul, li -->
<!-- 4) img -->
 <!-- task #2 one list, one local image, one remote image-->
<!-- 5) table, tr, th, td, a -->
<!-- 6) form, label, input, select -->
 <!-- task #3 form with h1 rate us + 3 inputs (select + radio buttons) & submit button -->


<!-- 1) html, head, body, title -->


<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON> - 70672800</title>
    <link rel="stylesheet" href="../day3_css/explaination-code.css">

</head>
<body>
<!-- 2) div, h1, h2, h3, p, a -->
 
    <div>
        <h1>Balah el Shaam Restaurant Menu</h1>
        <p>Find the inner happeniess (stomach happeniess)</p>
        <a href="https://www.youtube.com/watch?v=Y6AODOcOLDU">Follow us on Instagram!</a>
        <div>
            <h2>Our Locations</h2>
<!-- 3) ol, ul, li -->
            <ul>
                <li>Al Sadd</li>
                <li>Al Wakrah</li>
                <li>Al Khor</li>
            </ul>
<!-- 4) image -->
            <img src="qatar_map.jpg" alt="Qatar Map" width="200">
            <h2>Appetizers</h2>
<!-- 5) table, tr, th, td, a -->
            <table border="1">
                <tr>
                    <td>Spring Rolls</td>
                    <td>$5.99</td>
                </tr>
                <tr>
                    <td>Garlic Bread</td>
                    <td>$3.99</td>
                </tr>
            </table>
        </div>
        
        <div>
            <h2>Main Courses</h2>
            <table>
                <tr>
                    <td>Grilled Chicken</td>
                    <td>$12.99</td>
                </tr>
                <tr>
                    <td>Beef Steak</td>
                    <td>$15.99</td>
                </tr>
            </table>
        </div>
        
        <div>
            <h2>Desserts</h2>
            <table>
                <tr>
                    <td>Cheesecake</td>
                    <td>$6.99</td>
                </tr>
                <tr>
                    <td>Chocolate Cake</td>
                    <td>$5.99</td>
                </tr>
            </table>
        </div>
        
        <div>
            <h2>Beverages</h2>
            <table>
                <tr>
                    <td>Coffee</td>
                    <td>$2.99</td>
                </tr>
                <tr>
                    <td>Tea</td>
                    <td>$2.49</td>
                </tr>
            </table>
        </div>
        
        <div>
            <h2>Additional Information</h2>
            <details>
                <summary>More about our restaurant</summary>
                <p>We offer a variety of dishes made from fresh ingredients.</p>
            </details>
        </div>
        
        <div>
            <!-- 6) form, label, input, select -->
            <h2>Rate Us!</h2>
            <form>
                
                <label for="name">Name:</label>
                <input type="text" id="name" name="name"><br><br>
                <label for="dish">Rating:</label>
                <h2>what did you order?</h2>
                <label for="order">What did you order?</label><br>
                <input type="radio" id="appetizer" name="order" value="appetizer">
                <label for="appetizer">Appetizer</label><br>
                <input type="radio" id="main_course" name="order" value="main_course">
                <label for="main_course">Main Course</label><br>
                <input type="radio" id="dessert" name="order" value="dessert">
                <label for="dessert">Dessert</label><br>
                <input type="radio" id="beverage" name="order" value="beverage">
                <label for="beverage">Beverage</label><br>
                <select id="rating" name="Rating">
                    <option value="1">Very Bad</option>
                    <option value="2">Bad</option>
                    <option value="3">Average</option>
                    <option value="4">good</option>
                    <option value="5">Excelent</option>
                </select>
                <!-- 7) br, button, audio,  iframe (Copied from youtube share button)-->
                <br><br>
                <button type="submit">Submit</button>
            
            </form>
        </div>
        
        <div>
            <h2>Media</h2>
            <audio controls>
                <source src="poetry_audio.mp3" type="audio/mpeg">
                Your browser does not support the audio element.
            </audio>
            <iframe width="560" height="315" src="https://www.youtube.com/embed/0iHtmuTvcvo?si=RcWK3L1PcsdjLf4i" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
            </div>
            <br><br>
