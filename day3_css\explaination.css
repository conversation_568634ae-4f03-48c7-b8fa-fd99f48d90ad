/*  Styling Language */
/* 1) css syntax: one Selector {propeory: value;} */
    /* link html element */
    /* Selectors: element - .class - #id (one id per tag) or everything (*) */
/* 2) selectors cobinations */
    /* selector1 AND selector2 -- selector1.selector2 */
    /* selector1 ParentOf selector2 -- selector1 selector2 (element with selector 2 will change)*/
    /* 2 selectors with common properties .name1, .name2 {properties in common} */
/* 3) select colors: https://www.w3schools.com/colors/colors_picker.asp */
/* 4) height, width, padding, margin, border (explain with image) */
/* 5) font-size, border-radius, cursor, font-weight, font-family, text-align */

/* General Styles */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f9f9f9;
    color: #333;
    line-height: 1.6;
}

/* Header Styles */
header {
    background-color: #333;
    color: white;
    padding: 1em 0;
    text-align: center;
}

nav {
    display: flex;
    justify-content: center;
    gap: 20px;
}

nav a {
    color: white;
    text-decoration: none;
    font-weight: bold;
}

nav a:hover {
    text-decoration: underline;
}



section#home {
    background-color: #e8f5e9;
    padding: 1.5em;
    border-radius: 5px;
}

section#form {
    background-color: #fce4ec;
    padding: 1.5em;
    border-radius: 5px;
}

section#media {
    background-color: #e3f2fd;
    padding: 1.5em;
    border-radius: 5px;
}

section#contact {
    background-color: #fff3e0;
    padding: 1.5em;
    border-radius: 5px;
}

/* Form Styles */
form {
    margin-top: 20px;
    padding: 10px;
    background: #fff;
    border-radius: 5px;
    box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);
}

label {
    display: block;
    margin-bottom: 5px;
}

input, select, button {
    margin-bottom: 10px;
    padding: 8px;
    width: 100%;
    border: 1px solid #ddd;
    border-radius: 4px;
}

button {
    background-color: #4CAF50;
    color: white;
    /* border: none; */
    cursor: pointer;
}

button:hover {
    background-color: #45a049;
}

/* Media Styles */
iframe {
    display: block;
    margin: 20px auto;
    border: none;
}

audio {
    display: block;
    margin: 20px auto;
    width: 100%;
}

/* Footer Styles */
footer {
    background-color: #333;
    color: white;
    text-align: center;
    padding: 1em 0;
    margin-top: 20px;
}

/* Image Centering */
img {
    display: block;
    max-width: 100%;
    height: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Utility Classes */
.text-center {
    text-align: center;
}