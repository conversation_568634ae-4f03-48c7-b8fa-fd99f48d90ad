//1) Variables and their types
//2) Arithmetic and Boolean Operations
//3) If-Else Statements
//4) For & While Loops
//5) Functions
//6) Libraries

// Code in HTML:     <script src="basics.js"></script>
var myMoney = 10; // int
var GPA = 3.5;   // double or float
var name = "obaida"; //String
var myBooks = ['physics 101', 'Data analytics', 'How to survive'];
var myGrades = [100, 100, 100, 100, 99];
var myList = [11, 10, 9, 8]
GPA = 2;
// console.log("my old Name is: ", name);
name = '<PERSON>';
// console.log("my new Name is: ", name);
// --------------------------------------
// TASK #1 Helper:
var number1 = 15;
var number2  = 20;
var result = number1 + number2;
console.log("num1 + num2: ", result);
// - * /
// --------------------------------------

var isStudent = false; //boolean
var isProgrammer = true; //boolean

if(isStudent && isProgrammer){
    console.log(name, "is a student and a programmer");
}

if(!isStudent || isProgrammer){
    console.log(name, " is not a student or is a programmer");
}
if (isStudent){

}
if(GPA == 4.0){
    console.log("إنتا دحيح");
} else if (GPA > 3.0){
    console.log("جيد جداً");
} else {
    console.log('راسب');
}
/*
Greater than >
Less thaan <
Greater than or equal >=
Less than or equal <=
isEqual? ==

TASK 2:
اكتب متغيرين
isStudent
GPA
لو طالب افعل التالي:
    لو معدله 4، اكتبله ممتاز
    لو معدله بين ال4 وال3 اكتبله جيد جداً
    لو معدله بين ال3 وال2 اكتبله جيد
    لو معدله أقل من 2 اكتبله راسب
لو مش طالب اكتبله إنتا مش طالب
*/
//

