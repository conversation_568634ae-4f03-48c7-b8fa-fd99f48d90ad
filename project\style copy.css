/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Inter', sans-serif;
    scroll-behavior: smooth;
}


/* Header styles */
header {
    background-color: #333;
    padding: 1rem 2rem;
    position: sticky;
    top: 0px;
    text-align: center;
    z-index: 100;
    /* justify-content: center; */
}
header a{
    padding: 20px;
    text-decoration: none;
    color: #fff;
}
header a:hover{
    text-decoration: none;
    background-color: #fff;
    color: #333;
}

#home{
    background-color: #fff;
    text-align: center;
    padding: 2rem;
}

/* Contact section */
#contact {
    padding: 2rem;
    max-width: 600px;
    margin: 0 auto;
}

#contact h2 {
    text-align: center;
    font-size: 2rem;
    margin-bottom: 2rem;
    color: #333;
}

.contact-form {
    background-color: #f9f9f9;
    padding: 2rem;
    border-radius: 8px;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #333;
}

.submit-btn {
    width: 100%;
    padding: 0.75rem;
    background-color: #333;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;
}

.submit-btn:hover {
    background-color: #555;
}


