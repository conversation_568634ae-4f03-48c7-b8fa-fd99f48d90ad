<!-- يا هلا الشباب، اليوم التاني مهامه كالتالي:
- مهمة #1:  قم ببناء موقع إلكتروني فيه form بداخلها خانة لكتابة الإيميل وخانة لكتابة كلمة السر وزر Sign In،
 وتحت الاستبيان قم بإضافة فيديو يوتيوب وملف صوتي من الإنترنت (من موقع زي anasheed.info)

- مهمة #2: اقسم الموقع إلى قسمين sections، القسم الأول للاستبيان والقسم الثاني للفيديو والملف الصوتي، ثم أضف ترويسة Header فيها nav يقودك إلى القسمين عند الضغط عليهما -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0"> 
    <title>Document</title>
</head>
<body>
    <header>
        <nav>
            <a href="#media">Media</a>
            <a href="#form">Form</a>
        </nav>
    </header>
<section id="media">
    <iframe width="560" height="315" src="https://www.youtube.com/embed/8AFCU8KaHu4?si=sLHXDY88eFm4FuW0" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
    <br>
    <iframe width="560" height="315" src="https://www.youtube.com/embed/8AFCU8KaHu4?si=sLHXDY88eFm4FuW0" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
    <br>
    <iframe width="560" height="315" src="https://www.youtube.com/embed/8AFCU8KaHu4?si=sLHXDY88eFm4FuW0" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
    <br>
    <iframe width="560" height="315" src="https://www.youtube.com/embed/8AFCU8KaHu4?si=sLHXDY88eFm4FuW0" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
</section>

<section id="form">

    <form action=""> 
        
        <label for="">Enter ur Email</label>
        <input type="email">
        <button>Submit</button>
    </form>
</section>
    

</body>
</html>